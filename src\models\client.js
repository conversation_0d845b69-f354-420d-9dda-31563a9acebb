const { getPool } = require('../database');
const pool = getPool();

/**
 * 客户端模型
 */
class Client {
  /**
   * 创建或更新客户端
   * @param {Object} data - 客户端数据
   * @returns {Promise<Object>} - 客户端信息
   */
  static async createOrUpdate(data) {
    try {
      const { client_id, client_name, client_ip } = data;
      
      // 检查客户端表是否存在
      await pool.query(`
        CREATE TABLE IF NOT EXISTS clients (
          id INT AUTO_INCREMENT PRIMARY KEY,
          client_id VARCHAR(255) NOT NULL UNIQUE,
          client_name VARCHAR(255) NOT NULL,
          client_ip VARCHAR(255) NOT NULL,
          last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
      `);
      
      // 检查客户端是否已存在
      const [existingClients] = await pool.query(
        'SELECT * FROM clients WHERE client_id = ?',
        [client_id]
      );
      
      if (existingClients.length > 0) {
        // 更新客户端信息
        await pool.query(
          'UPDATE clients SET client_name = ?, client_ip = ?, last_active = CURRENT_TIMESTAMP WHERE client_id = ?',
          [client_name, client_ip, client_id]
        );
        
        return {
          ...existingClients[0],
          client_name,
          client_ip,
          last_active: new Date()
        };
      } else {
        // 创建新客户端
        const [result] = await pool.query(
          'INSERT INTO clients (client_id, client_name, client_ip) VALUES (?, ?, ?)',
          [client_id, client_name, client_ip]
        );
        
        return {
          id: result.insertId,
          client_id,
          client_name,
          client_ip,
          last_active: new Date(),
          created_at: new Date()
        };
      }
    } catch (error) {
      console.error('创建或更新客户端失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取所有客户端
   * @returns {Promise<Array>} - 客户端数组
   */
  static async findAll() {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM clients ORDER BY last_active DESC'
      );
      
      return rows;
    } catch (error) {
      console.error('获取客户端失败:', error);
      throw error;
    }
  }
  
  /**
   * 根据客户端ID获取客户端
   * @param {string} clientId - 客户端ID
   * @returns {Promise<Object|null>} - 客户端信息
   */
  static async findByClientId(clientId) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM clients WHERE client_id = ?',
        [clientId]
      );
      
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('获取客户端失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新客户端活跃状态
   * @param {string} clientId - 客户端ID
   * @param {string} clientIp - 客户端IP
   * @returns {Promise<boolean>} - 更新结果
   */
  static async updateActivity(clientId, clientIp) {
    try {
      await pool.query(
        'UPDATE clients SET client_ip = ?, last_active = CURRENT_TIMESTAMP WHERE client_id = ?',
        [clientIp, clientId]
      );
      
      return true;
    } catch (error) {
      console.error('更新客户端活跃状态失败:', error);
      throw error;
    }
  }
  
  /**
   * 删除客户端
   * @param {string} clientId - 客户端ID
   * @returns {Promise<boolean>} - 删除结果
   */
  static async delete(clientId) {
    try {
      await pool.query(
        'DELETE FROM clients WHERE client_id = ?',
        [clientId]
      );
      
      return true;
    } catch (error) {
      console.error('删除客户端失败:', error);
      throw error;
    }
  }
  
  /**
   * 获取客户端统计信息
   * @returns {Promise<Object>} - 统计信息
   */
  static async getStats() {
    try {
      const [totalCount] = await pool.query('SELECT COUNT(*) as count FROM clients');
      const [activeCount] = await pool.query('SELECT COUNT(*) as count FROM clients WHERE last_active > DATE_SUB(NOW(), INTERVAL 1 DAY)');
      
      return {
        total: totalCount[0].count,
        active: activeCount[0].count
      };
    } catch (error) {
      console.error('获取客户端统计信息失败:', error);
      throw error;
    }
  }
}

module.exports = Client; 