const { getPool } = require('../database');
const pool = getPool();

/**
 * 扫描结果模型
 */
class ScanResult {
  /**
   * 安全转换为JSON字符串
   * @param {any} value - 要转换的值
   * @returns {string} - JSON字符串
   */
  static safeStringify(value) {
    try {
      // 如果已经是字符串，检查是否是有效的JSON
      if (typeof value === 'string') {
        // 尝试解析然后重新字符串化以确保有效
        JSON.parse(value);
        return value;
      }
      // 否则转换为JSON字符串
      return JSON.stringify(value);
    } catch (error) {
      // 出错时返回空对象的JSON字符串
      console.error('JSON转换错误:', error.message);
      return '{}';
    }
  }

  /**
   * 安全解析JSON字符串
   * @param {string} json - JSON字符串
   * @returns {Object} - 解析后的对象
   */
  static safeParse(json) {
    try {
      // 处理无效值
      if (!json) return {};

      // 处理[object Object]特殊情况
      if (json === '[object Object]') return {};

      // 如果已经是对象，直接返回
      if (typeof json === 'object') return json;

      // 尝试解析JSON字符串
      return JSON.parse(json);
    } catch (error) {
      console.error('JSON解析错误:', error.message);
      return {};
    }
  }

  /**
   * 创建或更新扫描结果（去重）
   * @param {Object} data - 扫描结果数据
   * @returns {Promise<Object>} - 创建或更新的扫描结果
   */
  static async createOrUpdate(data) {
    try {
      const { host, port, status, connect_time, status_info, key_values, is_ransomware, auto_detected, client_ip, client_id } = data;

      // 安全地转换key_values为JSON字符串
      const keyValuesJson = this.safeStringify(key_values);

      // 检查是否已存在相同主机和端口的记录
      const [existingRecords] = await pool.query(
        'SELECT * FROM scan_results WHERE host = ? AND port = ?',
        [host, port]
      );

      if (existingRecords.length > 0) {
        // 记录已存在，更新为最新数据
        await pool.query(
          `UPDATE scan_results
           SET status = ?,
               connect_time = ?,
               status_info = ?,
               key_values = ?,
               is_ransomware = ?,
               auto_detected = ?,
               client_ip = ?,
               client_id = ?,
               created_at = CURRENT_TIMESTAMP
           WHERE host = ? AND port = ?`,
          [status, connect_time, status_info, keyValuesJson, is_ransomware, auto_detected, client_ip, client_id, host, port]
        );

        return {
          id: existingRecords[0].id,
          host,
          port,
          status,
          connect_time,
          status_info,
          key_values: this.safeParse(keyValuesJson),
          is_ransomware,
          auto_detected,
          client_ip,
          client_id,
          created_at: new Date()
        };
      } else {
        // 创建新记录
        const [result] = await pool.query(
          `INSERT INTO scan_results
           (host, port, status, connect_time, status_info, key_values, is_ransomware, auto_detected, client_ip, client_id)
           VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [host, port, status, connect_time, status_info, keyValuesJson, is_ransomware, auto_detected, client_ip, client_id]
        );

        return {
          id: result.insertId,
          host,
          port,
          status,
          connect_time,
          status_info,
          key_values: this.safeParse(keyValuesJson),
          is_ransomware,
          auto_detected,
          client_ip,
          client_id,
          created_at: new Date()
        };
      }
    } catch (error) {
      console.error('创建或更新扫描结果失败:', error);
      throw error;
    }
  }

  /**
   * 创建新的扫描结果
   * @param {Object} data - 扫描结果数据
   * @returns {Promise<Object>} - 创建的扫描结果
   */
  static async create(data) {
    // 使用去重方法创建结果
    return this.createOrUpdate(data);
  }

  /**
   * 批量创建扫描结果（去重）
   * @param {Array} dataArray - 扫描结果数据数组
   * @param {Object} clientInfo - 客户端信息
   * @returns {Promise<Array>} - 创建的扫描结果数组
   */
  static async createBatch(dataArray, clientInfo = {}) {
    try {
      const { client_ip, client_id } = clientInfo;
      const createdResults = [];

      // 使用事务确保原子性
      const connection = await pool.getConnection();
      await connection.beginTransaction();

      try {
        for (const data of dataArray) {
          const result = await this.createOrUpdate({
            ...data,
            client_ip,
            client_id
          });

          createdResults.push(result);
        }

        await connection.commit();
        connection.release();
        return createdResults;
      } catch (error) {
        await connection.rollback();
        connection.release();
        throw error;
      }
    } catch (error) {
      console.error('批量创建扫描结果失败:', error);
      throw error;
    }
  }

  /**
   * 获取所有扫描结果
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} - 扫描结果数组
   */
  static async findAll(options = {}) {
    try {
      const { limit = 100, offset = 0, sortBy = 'created_at', sortOrder = 'DESC' } = options;

      // 验证排序字段，防止SQL注入
      const allowedSortFields = ['id', 'host', 'port', 'status', 'created_at', 'is_ransomware', 'auto_detected'];
      const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
      const safeSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

      // 优化查询：只在需要时选择key_values字段，减少内存使用
      const selectFields = options.includeKeyValues !== false
        ? '*'
        : 'id, host, port, status, connect_time, status_info, is_ransomware, auto_detected, client_ip, client_id, created_at';

      const [rows] = await pool.query(
        `SELECT ${selectFields} FROM scan_results
         ORDER BY ${safeSortBy} ${safeSortOrder}
         LIMIT ? OFFSET ?`,
        [limit, offset]
      );

      return rows.map(row => ({
        ...row,
        key_values: row.key_values ? this.safeParse(row.key_values) : {}
      }));
    } catch (error) {
      console.error('获取扫描结果失败:', error);
      throw error;
    }
  }

  /**
   * 获取扫描结果列表（轻量级，不包含key_values）
   * @param {Object} options - 查询选项
   * @returns {Promise<Array>} - 扫描结果数组
   */
  static async findAllLightweight(options = {}) {
    try {
      const { limit = 100, offset = 0, sortBy = 'created_at', sortOrder = 'DESC' } = options;

      // 验证排序字段，防止SQL注入
      const allowedSortFields = ['id', 'host', 'port', 'status', 'created_at', 'is_ransomware', 'auto_detected'];
      const safeSortBy = allowedSortFields.includes(sortBy) ? sortBy : 'created_at';
      const safeSortOrder = ['ASC', 'DESC'].includes(sortOrder.toUpperCase()) ? sortOrder.toUpperCase() : 'DESC';

      // 轻量级查询：排除key_values字段以减少内存使用和排序负担
      const [rows] = await pool.query(
        `SELECT id, host, port, status, connect_time, status_info,
                is_ransomware, auto_detected, client_ip, client_id, created_at
         FROM scan_results
         ORDER BY ${safeSortBy} ${safeSortOrder}
         LIMIT ? OFFSET ?`,
        [limit, offset]
      );

      return rows;
    } catch (error) {
      console.error('获取扫描结果失败:', error);
      throw error;
    }
  }

  /**
   * 修复数据库中的无效key_values数据
   * @returns {Promise<void>}
   */
  static async fixInvalidKeyValues() {
    try {
      // 查找所有包含[object Object]的记录
      const [invalidRows] = await pool.query(
        "SELECT id FROM scan_results WHERE key_values = '[object Object]'"
      );

      if (invalidRows.length > 0) {
        console.log(`修复${invalidRows.length}条无效的key_values记录`);

        // 批量更新为空对象
        await pool.query(
          "UPDATE scan_results SET key_values = '{}' WHERE key_values = '[object Object]'"
        );
      }
    } catch (error) {
      console.error('修复无效key_values数据失败:', error);
    }
  }

  /**
   * 根据ID获取扫描结果
   * @param {number} id - 扫描结果ID
   * @returns {Promise<Object|null>} - 扫描结果
   */
  static async findById(id) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM scan_results WHERE id = ?',
        [id]
      );

      if (rows.length === 0) {
        return null;
      }

      const result = rows[0];
      return {
        ...result,
        key_values: this.safeParse(result.key_values)
      };
    } catch (error) {
      console.error('获取扫描结果失败:', error);
      throw error;
    }
  }

  /**
   * 根据主机和端口获取扫描结果
   * @param {string} host - 主机地址
   * @param {number} port - 端口号
   * @returns {Promise<Object|null>} - 扫描结果
   */
  static async findByHostAndPort(host, port) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM scan_results WHERE host = ? AND port = ?',
        [host, port]
      );

      if (rows.length === 0) {
        return null;
      }

      const result = rows[0];
      return {
        ...result,
        key_values: this.safeParse(result.key_values)
      };
    } catch (error) {
      console.error('获取扫描结果失败:', error);
      throw error;
    }
  }

  /**
   * 获取统计信息
   * @returns {Promise<Object>} - 统计信息
   */
  static async getStats() {
    try {
      const [totalCount] = await pool.query('SELECT COUNT(*) as count FROM scan_results');
      const [ransomwareCount] = await pool.query('SELECT COUNT(*) as count FROM scan_results WHERE is_ransomware = 1');
      const [autoDetectedCount] = await pool.query('SELECT COUNT(*) as count FROM scan_results WHERE auto_detected = 1');

      return {
        total: totalCount[0].count,
        ransomware: ransomwareCount[0].count,
        autoDetected: autoDetectedCount[0].count
      };
    } catch (error) {
      console.error('获取统计信息失败:', error);
      throw error;
    }
  }
}

module.exports = ScanResult;