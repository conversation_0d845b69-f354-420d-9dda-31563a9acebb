/**
 * 错误处理中间件
 */
const errorHandler = (err, req, res, next) => {
  // 设置默认状态码和错误消息
  const statusCode = err.statusCode || 500;
  const message = err.message || '服务器内部错误';
  
  // 日志记录错误
  console.error(`[${new Date().toISOString()}] ${statusCode} - ${message}`);
  if (err.stack) {
    console.error(err.stack);
  }
  
  // 返回错误响应
  res.status(statusCode).json({
    success: false,
    message,
    // 在开发环境下返回错误堆栈
    ...(process.env.NODE_ENV === 'development' && { stack: err.stack })
  });
};

/**
 * 404错误处理中间件
 */
const notFoundHandler = (req, res) => {
  res.status(404).json({
    success: false,
    message: '请求的资源不存在'
  });
};

module.exports = {
  errorHandler,
  notFoundHandler
}; 