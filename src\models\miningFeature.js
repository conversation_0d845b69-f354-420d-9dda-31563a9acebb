const { getPool } = require('../database');
const pool = getPool();

/**
 * 挖矿特征模型
 */
class MiningFeature {
  /**
   * 创建或更新挖矿特征（去重）
   * @param {Object} data - 挖矿特征数据
   * @returns {Promise<Object>} - 创建或更新的挖矿特征
   */
  static async createOrUpdate(data) {
    try {
      const { feature_text, match_type = 'exact' } = data;
      
      // 检查是否已存在相同特征
      const [existingFeatures] = await pool.query(
        'SELECT * FROM mining_features WHERE feature_text = ? AND match_type = ?',
        [feature_text, match_type]
      );
      
      if (existingFeatures.length > 0) {
        // 特征已存在，更新时间戳
        await pool.query(
          'UPDATE mining_features SET updated_at = CURRENT_TIMESTAMP WHERE id = ?',
          [existingFeatures[0].id]
        );
        
        return {
          ...existingFeatures[0],
          updated_at: new Date()
        };
      } else {
        // 创建新特征
        const [result] = await pool.query(
          'INSERT INTO mining_features (feature_text, match_type) VALUES (?, ?)',
          [feature_text, match_type]
        );
        
        return {
          id: result.insertId,
          feature_text,
          match_type,
          created_at: new Date(),
          updated_at: new Date()
        };
      }
    } catch (error) {
      console.error('创建或更新挖矿特征失败:', error);
      throw error;
    }
  }
  
  /**
   * 创建新的挖矿特征
   * @param {Object} data - 挖矿特征数据
   * @returns {Promise<Object>} - 创建的挖矿特征
   */
  static async create(data) {
    // 使用去重方法创建特征
    return this.createOrUpdate(data);
  }
  
  /**
   * 获取所有挖矿特征
   * @returns {Promise<Array>} - 挖矿特征数组
   */
  static async findAll() {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM mining_features ORDER BY created_at DESC'
      );
      
      return rows;
    } catch (error) {
      console.error('获取挖矿特征失败:', error);
      throw error;
    }
  }
  
  /**
   * 根据ID获取挖矿特征
   * @param {number} id - 挖矿特征ID
   * @returns {Promise<Object|null>} - 挖矿特征
   */
  static async findById(id) {
    try {
      const [rows] = await pool.query(
        'SELECT * FROM mining_features WHERE id = ?',
        [id]
      );
      
      return rows.length > 0 ? rows[0] : null;
    } catch (error) {
      console.error('获取挖矿特征失败:', error);
      throw error;
    }
  }
  
  /**
   * 更新挖矿特征
   * @param {number} id - 挖矿特征ID
   * @param {Object} data - 更新数据
   * @returns {Promise<boolean>} - 更新结果
   */
  static async update(id, data) {
    try {
      const { feature_text, match_type } = data;
      
      // 检查是否与其他特征冲突
      const [existingFeatures] = await pool.query(
        'SELECT * FROM mining_features WHERE feature_text = ? AND match_type = ? AND id != ?',
        [feature_text, match_type, id]
      );
      
      if (existingFeatures.length > 0) {
        throw new Error('已存在相同的特征文本和匹配类型');
      }
      
      await pool.query(
        'UPDATE mining_features SET feature_text = ?, match_type = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
        [feature_text, match_type, id]
      );
      
      return true;
    } catch (error) {
      console.error('更新挖矿特征失败:', error);
      throw error;
    }
  }
  
  /**
   * 删除挖矿特征
   * @param {number} id - 挖矿特征ID
   * @returns {Promise<boolean>} - 删除结果
   */
  static async delete(id) {
    try {
      await pool.query(
        'DELETE FROM mining_features WHERE id = ?',
        [id]
      );
      
      return true;
    } catch (error) {
      console.error('删除挖矿特征失败:', error);
      throw error;
    }
  }
  
  /**
   * 批量创建挖矿特征（去重）
   * @param {Array} features - 挖矿特征数组
   * @returns {Promise<boolean>} - 创建结果
   */
  static async createBatch(features) {
    try {
      if (!features || !Array.isArray(features) || features.length === 0) {
        return false;
      }
      
      // 使用事务确保原子性
      const connection = await pool.getConnection();
      await connection.beginTransaction();
      
      try {
        for (const feature of features) {
          await this.createOrUpdate(feature);
        }
        
        await connection.commit();
        connection.release();
        return true;
      } catch (error) {
        await connection.rollback();
        connection.release();
        throw error;
      }
    } catch (error) {
      console.error('批量创建挖矿特征失败:', error);
      throw error;
    }
  }
}

module.exports = MiningFeature; 