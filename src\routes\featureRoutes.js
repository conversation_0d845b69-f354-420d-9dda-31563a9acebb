const express = require('express');
const FeatureController = require('../controllers/featureController');
const { apiKeyAuth } = require('../middlewares/auth');

const router = express.Router();

// 获取所有特征 - 需要API密钥验证
router.get('/', apiKeyAuth, FeatureController.getAllFeatures);

// 创建新特征 - 需要API密钥验证
router.post('/', apiKeyAuth, FeatureController.createFeature);

// 批量创建特征 - 需要API密钥验证
router.post('/batch', apiKeyAuth, FeatureController.createBatchFeatures);

// 更新特征 - 需要API密钥验证
router.put('/:id', apiKeyAuth, FeatureController.updateFeature);

// 删除特征 - 需要API密钥验证
router.delete('/:id', apiKeyAuth, FeatureController.deleteFeature);

module.exports = router; 