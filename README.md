# Redis未授权远程检测服务器

这是一个用于检测Redis未授权访问和挖矿风险的服务器端应用。

## 项目结构

```
├── src/
│   ├── config/          # 配置文件
│   ├── controllers/     # 业务逻辑控制器
│   ├── middlewares/     # 中间件
│   ├── models/          # 数据模型
│   ├── routes/          # 路由定义
│   ├── app.js           # Express应用设置
│   └── server.js        # 服务器入口
├── package.json         # 项目依赖
├── api.md               # API文档
└── README.md            # 项目说明
```

## 特性

- 接收并存储Redis未授权扫描结果
- 管理挖矿特征库
- 使用API密钥进行认证
- MySQL数据库存储
- RESTful API接口

## 认证机制

本项目使用**API密钥认证**方式，所有API接口均需要在请求头中包含有效的API密钥：

```
X-API-Key: your_api_key_here
```

API密钥在`src/config/config.js`文件中手动配置：

```javascript
// API 密钥 (由开发者手动配置)
apiKey: 'redis_scanner_api_key_2025' // 默认API密钥，建议在生产环境中更改为更复杂的值
```

## 安装

1. 克隆仓库
2. 安装依赖

```bash
npm install
```

3. 配置环境变量

创建`.env`文件并设置以下变量：

```
PORT=3000
NODE_ENV=development

# 数据库配置
DB_HOST=localhost
DB_USER=root
DB_PASSWORD=your_password
DB_NAME=redis_scanner
DB_PORT=3306
```

4. 修改API密钥

编辑`src/config/config.js`文件，修改默认API密钥为您自己的密钥。

5. 启动服务器

```bash
# 开发环境
npm run dev

# 生产环境
npm start
```

## API文档

详细的API文档请参阅[api.md](api.md)文件。

## 客户端集成

参考API文档中的客户端集成示例部分，了解如何在Node.js和Python中集成此服务。

## 常见问题解决

### "Out of sort memory" 错误解决方案

如果在同步结果时遇到 "Out of sort memory, consider increasing server sort buffer size" 错误，请按以下步骤解决：

#### 方法1：临时增加MySQL排序缓冲区（推荐）
```bash
# 连接到MySQL数据库
mysql -u root -p

# 查看当前sort_buffer_size设置
SHOW VARIABLES LIKE '%sort_buffer_size%';

# 临时增加排序缓冲区大小（重启后失效）
SET GLOBAL sort_buffer_size = 2097152;  # 2MB
# 或者设置更大的值
SET GLOBAL sort_buffer_size = 4194304;  # 4MB

# 退出MySQL
EXIT;
```

#### 方法2：永久修改MySQL配置
编辑MySQL配置文件（通常是 `/etc/mysql/my.cnf` 或 `/etc/my.cnf`）：
```ini
[mysqld]
sort_buffer_size = 2M
max_sort_length = 1024
```

重启MySQL服务：
```bash
sudo systemctl restart mysql
```

## 注意事项

1. 在生产环境中部署前，请务必修改默认API密钥
2. 建议使用HTTPS加密传输数据
3. 定期备份数据库数据
4. 定期更新挖矿特征库