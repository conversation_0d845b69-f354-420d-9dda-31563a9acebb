const ScanResult = require('../models/scanResult');
const Client = require('../models/client');

/**
 * 扫描结果控制器
 */
class ScanController {
  /**
   * 接收扫描结果
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {Function} next - 下一个中间件
   */
  static async receiveScanResults(req, res, next) {
    try {
      const { results } = req.body;
      
      // 验证请求数据
      if (!results || !Array.isArray(results) || results.length === 0) {
        return res.status(400).json({
          success: false,
          message: '无效的请求数据'
        });
      }
      
      // 验证每个扫描结果的必填字段
      for (const result of results) {
        if (!result.host || result.port === undefined || result.status === undefined) {
          return res.status(400).json({
            success: false,
            message: '扫描结果缺少必填字段(host, port, status)'
          });
        }
        
        // 确保connect_time有效，如果无效则使用当前时间
        if (!result.connect_time) {
          result.connect_time = new Date().toISOString();
        }
        
        // 确保key_values是有效格式，如果无效则设为空对象
        if (result.key_values) {
          try {
            if (typeof result.key_values === 'string') {
              // 尝试解析字符串
              JSON.parse(result.key_values);
            } else {
              // 尝试转换非字符串
              JSON.stringify(result.key_values);
            }
          } catch (error) {
            console.error(`扫描结果key_values格式无效: ${error.message}`);
            result.key_values = {};
          }
        } else {
          result.key_values = {};
        }
      }
      
      // 获取客户端信息
      const clientInfo = {
        client_ip: req.ip,
        client_id: req.headers['x-client-id'] || 'unknown'
      };
      
      // 更新客户端活跃状态
      if (clientInfo.client_id !== 'unknown') {
        await Client.createOrUpdate({
          client_id: clientInfo.client_id,
          client_name: req.headers['x-client-name'] || clientInfo.client_id,
          client_ip: clientInfo.client_ip
        });
      }
      
      // 批量保存扫描结果
      await ScanResult.createBatch(results, clientInfo);
      
      res.status(200).json({
        success: true,
        message: '上传成功'
      });
    } catch (error) {
      console.error('接收扫描结果错误:', error);
      // 如果是已知错误类型，返回对应错误信息
      if (error.message && error.message.includes('JSON')) {
        return res.status(400).json({
          success: false,
          message: '无效的JSON数据格式',
          error: error.message
        });
      }
      next(error);
    }
  }
  
  /**
   * 获取所有扫描结果
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {Function} next - 下一个中间件
   */
  static async getAllScanResults(req, res, next) {
    try {
      const { limit = 100, offset = 0, sortBy = 'created_at', sortOrder = 'DESC' } = req.query;
      
      const results = await ScanResult.findAll({
        limit: parseInt(limit),
        offset: parseInt(offset),
        sortBy,
        sortOrder
      });
      
      res.status(200).json({
        success: true,
        count: results.length,
        results
      });
    } catch (error) {
      console.error('获取扫描结果错误:', error);
      next(error);
    }
  }
  
  /**
   * 根据ID获取扫描结果
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {Function} next - 下一个中间件
   */
  static async getScanResultById(req, res, next) {
    try {
      const { id } = req.params;
      
      const result = await ScanResult.findById(id);
      
      if (!result) {
        return res.status(404).json({
          success: false,
          message: '未找到指定的扫描结果'
        });
      }
      
      res.status(200).json({
        success: true,
        result
      });
    } catch (error) {
      console.error('获取扫描结果错误:', error);
      next(error);
    }
  }
  
  /**
   * 获取扫描结果统计信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {Function} next - 下一个中间件
   */
  static async getScanStats(req, res, next) {
    try {
      const stats = await ScanResult.getStats();
      
      res.status(200).json({
        success: true,
        stats
      });
    } catch (error) {
      console.error('获取扫描统计信息错误:', error);
      next(error);
    }
  }
}

module.exports = ScanController; 