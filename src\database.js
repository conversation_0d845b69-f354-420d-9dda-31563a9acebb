const mysql = require('mysql2/promise');
const config = require('./config/config');

// 创建数据库连接池
const pool = mysql.createPool({
  host: config.database.host,
  user: config.database.user,
  password: config.database.password,
  database: config.database.database,
  port: config.database.port,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 测试数据库连接
const testConnection = async () => {
  try {
    const connection = await pool.getConnection();
    console.log('数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error.message);
    return false;
  }
};

// 初始化数据库表
const initDatabase = async () => {
  try {
    // 测试连接
    const connected = await testConnection();
    if (!connected) {
      console.error('无法初始化数据库，连接失败');
      return;
    }

    // 创建扫描结果表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS scan_results (
        id INT AUTO_INCREMENT PRIMARY KEY,
        host VARCHAR(255) NOT NULL,
        port INT NOT NULL,
        status TINYINT NOT NULL,
        connect_time DATETIME NOT NULL,
        status_info TEXT,
        key_values JSON,
        is_ransomware TINYINT DEFAULT 0,
        auto_detected TINYINT DEFAULT 0,
        client_ip VARCHAR(255),
        client_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        INDEX (host, port)
      )
    `);
    console.log('扫描结果表初始化成功');

    // 检查是否存在host_port索引，如果不存在则添加
    try {
      const [indexes] = await pool.query(`
        SHOW INDEX FROM scan_results WHERE Key_name = 'host_port_unique'
      `);
      
      if (indexes.length === 0) {
        await pool.query(`
          CREATE UNIQUE INDEX host_port_unique ON scan_results(host, port)
        `);
        console.log('为扫描结果表添加唯一索引成功');
      }
    } catch (error) {
      console.error('检查或添加扫描结果唯一索引失败:', error.message);
    }

    // 创建挖矿特征表
    await pool.query(`
      CREATE TABLE IF NOT EXISTS mining_features (
        id INT AUTO_INCREMENT PRIMARY KEY,
        feature_text VARCHAR(255) NOT NULL,
        match_type ENUM('exact', 'regex') DEFAULT 'exact',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    console.log('挖矿特征表初始化成功');

    // 检查是否存在feature_unique索引，如果不存在则添加
    try {
      const [indexes] = await pool.query(`
        SHOW INDEX FROM mining_features WHERE Key_name = 'feature_unique'
      `);
      
      if (indexes.length === 0) {
        await pool.query(`
          CREATE UNIQUE INDEX feature_unique ON mining_features(feature_text, match_type)
        `);
        console.log('为挖矿特征表添加唯一索引成功');
      }
    } catch (error) {
      console.error('检查或添加挖矿特征唯一索引失败:', error.message);
    }

    // 检查是否需要创建默认特征
    const [features] = await pool.query('SELECT COUNT(*) as count FROM mining_features');
    if (features[0].count === 0) {
      // 插入默认特征
      await pool.query(`
        INSERT INTO mining_features (feature_text, match_type) VALUES
        ('miner', 'exact'),
        ('\\\\bxmr\\\\b', 'regex'),
        ('monero', 'exact'),
        ('cryptominer', 'exact'),
        ('mining_pool', 'exact')
      `);
      console.log('默认挖矿特征已创建');
    }
  } catch (error) {
    console.error('初始化数据库失败:', error.message);
  }
};

// 获取数据库连接池，供模型使用
const getPool = () => pool;

module.exports = {
  testConnection,
  initDatabase,
  getPool
}; 