const MiningFeature = require('../models/miningFeature');

/**
 * 特征控制器
 */
class FeatureController {
  /**
   * 获取所有特征
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {Function} next - 下一个中间件
   */
  static async getAllFeatures(req, res, next) {
    try {
      const features = await MiningFeature.findAll();
      
      res.status(200).json({
        success: true,
        features
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * 创建新特征
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {Function} next - 下一个中间件
   */
  static async createFeature(req, res, next) {
    try {
      const { feature_text, match_type = 'exact' } = req.body;
      
      // 验证请求数据
      if (!feature_text) {
        return res.status(400).json({
          success: false,
          message: '特征文本不能为空'
        });
      }
      
      const feature = await MiningFeature.create({
        feature_text,
        match_type
      });
      
      res.status(201).json({
        success: true,
        message: '特征创建成功',
        feature
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * 更新特征
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {Function} next - 下一个中间件
   */
  static async updateFeature(req, res, next) {
    try {
      const { id } = req.params;
      const { feature_text, match_type } = req.body;
      
      // 验证请求数据
      if (!feature_text) {
        return res.status(400).json({
          success: false,
          message: '特征文本不能为空'
        });
      }
      
      // 检查特征是否存在
      const existingFeature = await MiningFeature.findById(id);
      if (!existingFeature) {
        return res.status(404).json({
          success: false,
          message: '未找到指定的特征'
        });
      }
      
      // 更新特征
      await MiningFeature.update(id, {
        feature_text,
        match_type: match_type || existingFeature.match_type
      });
      
      res.status(200).json({
        success: true,
        message: '特征更新成功'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * 删除特征
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {Function} next - 下一个中间件
   */
  static async deleteFeature(req, res, next) {
    try {
      const { id } = req.params;
      
      // 检查特征是否存在
      const existingFeature = await MiningFeature.findById(id);
      if (!existingFeature) {
        return res.status(404).json({
          success: false,
          message: '未找到指定的特征'
        });
      }
      
      // 删除特征
      await MiningFeature.delete(id);
      
      res.status(200).json({
        success: true,
        message: '特征删除成功'
      });
    } catch (error) {
      next(error);
    }
  }
  
  /**
   * 批量创建特征
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   * @param {Function} next - 下一个中间件
   */
  static async createBatchFeatures(req, res, next) {
    try {
      const { features } = req.body;
      
      // 验证请求数据
      if (!features || !Array.isArray(features) || features.length === 0) {
        return res.status(400).json({
          success: false,
          message: '无效的请求数据'
        });
      }
      
      // 批量创建特征
      await MiningFeature.createBatch(features);
      
      res.status(201).json({
        success: true,
        message: '特征批量创建成功'
      });
    } catch (error) {
      next(error);
    }
  }
}

module.exports = FeatureController; 