# Redis未授权远程检测服务器 API 文档

本文档详细描述了Redis未授权远程检测服务器的API接口规范，供客户端集成使用。

## 基础信息

- **基础URL**: `http://your-server-domain:3000` (生产环境建议使用HTTPS)
- **数据格式**: 所有请求和响应均使用JSON格式
- **字符编码**: UTF-8
- **数据去重**: 系统对特征和扫描结果实现了自动去重功能

## 认证方式

API使用API密钥认证方式：

### API密钥认证

所有API接口都需要API密钥进行验证。

**请求头格式**:
```
X-API-Key: your_api_key_here
```

## 关于API密钥

API密钥由开发者在服务器配置文件中手动设置。在项目的`src/config/config.js`文件中，您可以找到默认的API密钥设置：

```javascript
// API 密钥 (由开发者手动配置)
apiKey: 'redis_scanner_api_key_2025'
```

## 数据处理特性

### 去重功能

1. **特征数据去重**：
   - 系统根据特征文本和匹配类型进行去重
   - 当提交相同特征时，系统会更新现有记录而非创建新记录
   - 自动保留最新的特征数据

2. **扫描结果去重**：
   - 系统根据主机(IP/域名)和端口组合进行去重
   - 当提交相同主机和端口的扫描结果时，系统会更新为最新数据
   - 自动覆盖旧的扫描结果

### JSON数据处理

系统对JSON数据有特殊处理，确保数据完整性：

- 如果提交的`key_values`字段格式无效，系统会自动转换为空对象`{}`
- 系统能够处理各种JSON格式(字符串或对象)，并进行安全转换
- 对于缺失的必填字段，系统会返回明确的错误信息

## API接口

### 1. 接收扫描结果

用于客户端上传Redis未授权扫描的结果数据。系统会自动对相同主机和端口的数据进行去重，保留最新记录。

**URL**: `/api/scan/results`

**方法**: POST

**认证**: API密钥 (X-API-Key)

**请求头**:
```
Content-Type: application/json
X-API-Key: your_api_key_here
X-Client-ID: your_client_id (可选)
X-Client-Name: your_client_name (可选)
```

**请求体**:
```json
{
  "results": [
    {
      "host": "***********",
      "port": 6379,
      "status": 1,
      "connect_time": "2023-05-27T10:00:00.000Z",
      "status_info": "连接成功，无需密码 | 自动检测到挖矿特征: miner (内置关键词)",
      "key_values": {
        "db0": {
          "key1": {
            "type": "string",
            "value": "value1"
          },
          "key2": {
            "type": "list",
            "value": ["item1", "item2"]
          }
        },
        "db1": {
          "key3": {
            "type": "hash",
            "value": {
              "field1": "value1",
              "field2": "value2"
            }
          }
        }
      },
      "is_ransomware": 1,
      "auto_detected": 1
    }
  ]
}
```

**参数说明**:

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| results | Array | 是 | 扫描结果数组 |
| host | String | 是 | 主机IP地址或域名 |
| port | Number | 是 | Redis端口 |
| status | Number | 是 | 连接状态(1=成功,0=失败) |
| connect_time | String | 否 | 连接时间，ISO格式，缺失时自动使用当前时间 |
| status_info | String | 否 | 状态详细信息 |
| key_values | Object | 否 | Redis数据库中的键值数据，无效格式会自动转为空对象 |
| is_ransomware | Number | 否 | 是否检测到勒索(1=是,0=否) |
| auto_detected | Number | 否 | 是否自动检测到威胁(1=是,0=否) |

**成功响应** (状态码: 200):
```json
{
  "success": true,
  "message": "上传成功"
}
```

**错误响应** (状态码: 400, 401, 500):
```json
{
  "success": false,
  "message": "错误详细信息"
}
```

**可能的错误消息**:

| 错误消息 | 描述 |
|---------|------|
| "无效的请求数据" | 请求体中的results不是有效的数组 |
| "扫描结果缺少必填字段(host, port, status)" | 扫描结果缺少必要字段 |
| "无效的JSON数据格式" | key_values字段不是有效的JSON格式 |

### 2. 获取挖矿特征库

用于客户端同步最新的挖矿特征库。

**URL**: `/api/features`

**方法**: GET

**认证**: API密钥 (X-API-Key)

**请求头**:
```
X-API-Key: your_api_key_here
```

**成功响应** (状态码: 200):
```json
{
  "success": true,
  "features": [
    {
      "id": 1,
      "feature_text": "miner",
      "match_type": "exact",
      "created_at": "2023-05-27T10:00:00.000Z",
      "updated_at": "2023-05-27T10:00:00.000Z"
    },
    {
      "id": 2,
      "feature_text": "\\bxmr\\b",
      "match_type": "regex",
      "created_at": "2023-05-27T10:00:00.000Z",
      "updated_at": "2023-05-27T10:00:00.000Z"
    }
  ]
}
```

**错误响应** (状态码: 401, 500):
```json
{
  "success": false,
  "message": "错误详细信息"
}
```

### 3. 创建新特征

添加新的挖矿特征到特征库。系统会自动对相同特征文本和匹配类型的记录进行去重，保留最新记录。

**URL**: `/api/features`

**方法**: POST

**认证**: API密钥 (X-API-Key)

**请求头**:
```
Content-Type: application/json
X-API-Key: your_api_key_here
```

**请求体**:
```json
{
  "feature_text": "cryptominer",
  "match_type": "exact"
}
```

**参数说明**:

| 字段名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| feature_text | String | 是 | 特征文本 |
| match_type | String | 否 | 匹配类型(exact=精确匹配,regex=正则表达式)，默认为exact |

**成功响应** (状态码: 201):
```json
{
  "success": true,
  "message": "特征创建成功",
  "feature": {
    "id": 3,
    "feature_text": "cryptominer",
    "match_type": "exact",
    "created_at": "2023-05-27T10:00:00.000Z",
    "updated_at": "2023-05-27T10:00:00.000Z"
  }
}
```

**错误响应** (状态码: 400, 401, 500):
```json
{
  "success": false,
  "message": "错误详细信息"
}
```

### 4. 批量创建特征

批量添加多个挖矿特征到特征库。系统会自动对每个特征进行去重处理，保留最新记录。

**URL**: `/api/features/batch`

**方法**: POST

**认证**: API密钥 (X-API-Key)

**请求头**:
```
Content-Type: application/json
X-API-Key: your_api_key_here
```

**请求体**:
```json
{
  "features": [
    {
      "feature_text": "mining_pool",
      "match_type": "exact"
    },
    {
      "feature_text": "\\bcoin\\b",
      "match_type": "regex"
    }
  ]
}
```

**成功响应** (状态码: 201):
```json
{
  "success": true,
  "message": "特征批量创建成功"
}
```

**错误响应** (状态码: 400, 401, 500):
```json
{
  "success": false,
  "message": "错误详细信息"
}
```

### 5. 更新特征

更新已有的挖矿特征。

**URL**: `/api/features/:id`

**方法**: PUT

**认证**: API密钥 (X-API-Key)

**请求头**:
```
Content-Type: application/json
X-API-Key: your_api_key_here
```

**请求体**:
```json
{
  "feature_text": "updated_miner",
  "match_type": "exact"
}
```

**成功响应** (状态码: 200):
```json
{
  "success": true,
  "message": "特征更新成功"
}
```

**错误响应** (状态码: 400, 401, 404, 500):
```json
{
  "success": false,
  "message": "错误详细信息"
}
```

### 6. 删除特征

删除指定的挖矿特征。

**URL**: `/api/features/:id`

**方法**: DELETE

**认证**: API密钥 (X-API-Key)

**请求头**:
```
X-API-Key: your_api_key_here
```

**成功响应** (状态码: 200):
```json
{
  "success": true,
  "message": "特征删除成功"
}
```

**错误响应** (状态码: 401, 404, 500):
```json
{
  "success": false,
  "message": "错误详细信息"
}
```

### 7. 获取所有扫描结果

获取所有Redis未授权扫描结果。由于系统实现了去重功能，结果将不包含重复的主机和端口组合。

**URL**: `/api/scan/results`

**方法**: GET

**认证**: API密钥 (X-API-Key)

**请求头**:
```
X-API-Key: your_api_key_here
```

**查询参数**:

| 参数名 | 类型 | 必填 | 描述 |
|--------|------|------|------|
| limit | Number | 否 | 返回结果数量限制，默认100 |
| offset | Number | 否 | 结果偏移量，用于分页，默认0 |
| sortBy | String | 否 | 排序字段，默认created_at |
| sortOrder | String | 否 | 排序方向(ASC或DESC)，默认DESC |

**成功响应** (状态码: 200):
```json
{
  "success": true,
  "count": 2,
  "results": [
    {
      "id": 1,
      "host": "***********",
      "port": 6379,
      "status": 1,
      "connect_time": "2023-05-27T10:00:00.000Z",
      "status_info": "连接成功，无需密码",
      "key_values": {
        "db0": {
          "key1": {
            "type": "string",
            "value": "value1"
          }
        }
      },
      "is_ransomware": 0,
      "auto_detected": 1,
      "client_ip": "127.0.0.1",
      "client_id": "client1",
      "created_at": "2023-05-27T10:00:00.000Z"
    },
    {
      "id": 2,
      "host": "***********",
      "port": 6379,
      "status": 1,
      "connect_time": "2023-05-27T11:00:00.000Z",
      "status_info": "连接成功，无需密码 | 自动检测到挖矿特征: miner (内置关键词)",
      "key_values": {
        "db0": {
          "key1": {
            "type": "string",
            "value": "miner"
          }
        }
      },
      "is_ransomware": 1,
      "auto_detected": 1,
      "client_ip": "127.0.0.1",
      "client_id": "client1",
      "created_at": "2023-05-27T11:00:00.000Z"
    }
  ]
}
```

**错误响应** (状态码: 401, 500):
```json
{
  "success": false,
  "message": "错误详细信息"
}
```

### 8. 获取单个扫描结果

根据ID获取单个扫描结果详情。

**URL**: `/api/scan/results/:id`

**方法**: GET

**认证**: API密钥 (X-API-Key)

**请求头**:
```
X-API-Key: your_api_key_here
```

**成功响应** (状态码: 200):
```json
{
  "success": true,
  "result": {
    "id": 1,
    "host": "***********",
    "port": 6379,
    "status": 1,
    "connect_time": "2023-05-27T10:00:00.000Z",
    "status_info": "连接成功，无需密码",
    "key_values": {
      "db0": {
        "key1": {
          "type": "string",
          "value": "value1"
        }
      }
    },
    "is_ransomware": 0,
    "auto_detected": 1,
    "client_ip": "127.0.0.1",
    "client_id": "client1",
    "created_at": "2023-05-27T10:00:00.000Z"
  }
}
```

**错误响应** (状态码: 401, 404, 500):
```json
{
  "success": false,
  "message": "错误详细信息"
}
```

### 9. 获取扫描统计信息

获取Redis未授权扫描的统计信息。

**URL**: `/api/scan/stats`

**方法**: GET

**认证**: API密钥 (X-API-Key)

**请求头**:
```
X-API-Key: your_api_key_here
```

**成功响应** (状态码: 200):
```json
{
  "success": true,
  "stats": {
    "total": 100,
    "ransomware": 25,
    "autoDetected": 75
  }
}
```

**错误响应** (状态码: 401, 500):
```json
{
  "success": false,
  "message": "错误详细信息"
}
```

## 错误码说明

| 状态码 | 描述 |
|--------|------|
| 200 | 请求成功 |
| 201 | 创建成功 |
| 400 | 请求参数错误或无效的JSON格式 |
| 401 | 认证失败 |
| 404 | 资源不存在 |
| 500 | 服务器内部错误 |

## 客户端集成示例

### Node.js 示例

```javascript
const axios = require('axios');

// 上传扫描结果
async function uploadScanResults(apiKey, results) {
  try {
    // 确保数据格式正确
    const validResults = results.map(result => ({
      ...result,
      // 确保connect_time有效，否则使用当前时间
      connect_time: result.connect_time || new Date().toISOString(),
      // 确保key_values是有效对象
      key_values: result.key_values || {}
    }));
    
    const response = await axios.post('http://your-server-domain:3000/api/scan/results', {
      results: validResults
    }, {
      headers: {
        'Content-Type': 'application/json',
        'X-API-Key': apiKey,
        'X-Client-ID': 'your-client-id',
        'X-Client-Name': 'your-client-name'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('上传扫描结果失败:', error.response?.data || error.message);
    throw error;
  }
}

// 获取特征库
async function getFeatures(apiKey) {
  try {
    const response = await axios.get('http://your-server-domain:3000/api/features', {
      headers: {
        'X-API-Key': apiKey
      }
    });
    
    return response.data.features;
  } catch (error) {
    console.error('获取特征库失败:', error.response?.data || error.message);
    throw error;
  }
}
```

### Python 示例

```python
import requests
import json
from datetime import datetime

# 上传扫描结果
def upload_scan_results(api_key, results, client_id=None, client_name=None):
    headers = {
        'Content-Type': 'application/json',
        'X-API-Key': api_key
    }
    
    if client_id:
        headers['X-Client-ID'] = client_id
    
    if client_name:
        headers['X-Client-Name'] = client_name
    
    # 确保数据格式正确
    valid_results = []
    for result in results:
        # 复制原始结果
        valid_result = result.copy()
        # 确保connect_time有效，否则使用当前时间
        if 'connect_time' not in valid_result or not valid_result['connect_time']:
            valid_result['connect_time'] = datetime.now().isoformat()
        # 确保key_values是有效对象
        if 'key_values' not in valid_result or not valid_result['key_values']:
            valid_result['key_values'] = {}
        valid_results.append(valid_result)
    
    payload = {
        'results': valid_results
    }
    
    try:
        response = requests.post(
            'http://your-server-domain:3000/api/scan/results',
            headers=headers,
            data=json.dumps(payload)
        )
        
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"上传扫描结果失败: {str(e)}")
        if hasattr(e, 'response') and e.response:
            print(f"服务器返回: {e.response.text}")
        raise

# 获取特征库
def get_features(api_key):
    headers = {
        'X-API-Key': api_key
    }
    
    try:
        response = requests.get(
            'http://your-server-domain:3000/api/features',
            headers=headers
        )
        
        response.raise_for_status()
        return response.json().get('features', [])
    except requests.exceptions.RequestException as e:
        print(f"获取特征库失败: {str(e)}")
        raise
```

## 注意事项

1. 所有API调用都应使用HTTPS以确保数据传输安全
2. API密钥应妥善保管，避免泄露
3. 客户端应实现适当的错误处理和重试机制
4. 上传的数据量过大时可能需要增加超时设置
5. 生产环境中建议使用负载均衡和CDN来提高API服务的可用性
6. 利用系统的去重功能避免提交重复数据，减少网络传输和服务器负载
7. 确保提交的JSON数据格式正确，特别是key_values字段

## 更新历史

| 日期 | 版本 | 描述 |
|------|------|------|
| 2025-05-27 | 1.0.0 | 初始版本 |
| 2025-05-28 | 1.1.0 | 移除JWT认证，仅使用API-KEY认证 |
| 2025-05-28 | 1.2.0 | 添加去重功能和增强的JSON处理 | 