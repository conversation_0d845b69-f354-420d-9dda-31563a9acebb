const mysql = require('mysql2/promise');
const config = require('./config');

// 创建数据库连接池
const pool = mysql.createPool({
  host: config.database.host,
  user: config.database.user,
  password: config.database.password,
  database: config.database.database,
  port: config.database.port,
  waitForConnections: true,
  connectionLimit: 10,
  queueLimit: 0
});

// 测试数据库连接
async function testConnection() {
  try {
    const connection = await pool.getConnection();
    console.log('数据库连接成功');
    connection.release();
    return true;
  } catch (error) {
    console.error('数据库连接失败:', error);
    return false;
  }
}

// 初始化数据库表
async function initDatabase() {
  try {
    const conn = await pool.getConnection();
    
    // 创建扫描结果表
    await conn.query(`
      CREATE TABLE IF NOT EXISTS scan_results (
        id INTEGER PRIMARY KEY AUTO_INCREMENT,
        host VARCHAR(255) NOT NULL,
        port INTEGER NOT NULL,
        status INTEGER NOT NULL,
        connect_time DATETIME NOT NULL,
        status_info TEXT,
        key_values JSON,
        is_ransomware INTEGER DEFAULT 0,
        auto_detected INTEGER DEFAULT 0,
        client_ip VARCHAR(255),
        client_id VARCHAR(255),
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // 创建挖矿特征表
    await conn.query(`
      CREATE TABLE IF NOT EXISTS mining_features (
        id INTEGER PRIMARY KEY AUTO_INCREMENT,
        feature_text TEXT NOT NULL,
        match_type VARCHAR(20) DEFAULT 'exact',
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
      )
    `);
    
    // 创建客户端表
    await conn.query(`
      CREATE TABLE IF NOT EXISTS clients (
        id INTEGER PRIMARY KEY AUTO_INCREMENT,
        client_id VARCHAR(255) NOT NULL,
        client_name VARCHAR(255),
        client_ip VARCHAR(255),
        last_active TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);
    
    // 添加一些默认的挖矿特征
    await conn.query(`
      INSERT INTO mining_features (feature_text, match_type)
      VALUES 
        ('miner', 'exact'),
        ('\\\\bxmr\\\\b', 'regex'),
        ('monero', 'exact'),
        ('stratum', 'exact'),
        ('\\\\bcoin\\\\b', 'regex')
      ON DUPLICATE KEY UPDATE updated_at = CURRENT_TIMESTAMP
    `);
    
    conn.release();
    console.log('数据库表初始化成功');
    return true;
  } catch (error) {
    console.error('数据库表初始化失败:', error);
    return false;
  }
}

module.exports = {
  pool,
  testConnection,
  initDatabase
}; 