const express = require('express');
const ScanController = require('../controllers/scanController');
const { apiKeyAuth } = require('../middlewares/auth');

const router = express.Router();

// 接收扫描结果接口 - 需要API密钥验证
router.post('/results', apiKeyAuth, ScanController.receiveScanResults);

// 获取所有扫描结果 - 需要API密钥验证
router.get('/results', apiKeyAuth, ScanController.getAllScanResults);

// 获取单个扫描结果 - 需要API密钥验证
router.get('/results/:id', apiKeyAuth, ScanController.getScanResultById);

// 获取扫描统计信息 - 需要API密钥验证
router.get('/stats', apiKeyAuth, ScanController.getScanStats);

module.exports = router; 