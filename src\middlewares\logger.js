const morgan = require('morgan');
const fs = require('fs');
const path = require('path');

// 确保logs目录存在
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
  fs.mkdirSync(logsDir, { recursive: true });
}

// 创建一个写入流
const accessLogStream = fs.createWriteStream(
  path.join(logsDir, 'access.log'),
  { flags: 'a' }
);

// 自定义日志格式
const logFormat = ':remote-addr - :remote-user [:date[clf]] ":method :url HTTP/:http-version" :status :res[content-length] ":referrer" ":user-agent" :response-time ms';

// 开发环境日志
const developmentLogger = morgan('dev');

// 生产环境日志
const productionLogger = morgan(logFormat, {
  stream: accessLogStream
});

// 根据环境选择日志中间件
const logger = (req, res, next) => {
  if (process.env.NODE_ENV === 'production') {
    return productionLogger(req, res, next);
  }
  return developmentLogger(req, res, next);
};

module.exports = logger; 