{"name": "redis-unauthorized-server", "version": "1.0.0", "description": "Redis未授权远程检测服务器", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest"}, "keywords": ["redis", "security", "scanner"], "author": "", "license": "ISC", "dependencies": {"cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "helmet": "^7.1.0", "morgan": "^1.10.0", "mysql2": "^3.6.5"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.2"}}