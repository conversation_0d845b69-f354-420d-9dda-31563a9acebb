const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const config = require('./config/config');
const { initDatabase } = require('./database');
const scanRoutes = require('./routes/scanRoutes');
const featureRoutes = require('./routes/featureRoutes');
const { errorHandler } = require('./middlewares/errorHandler');
const logger = require('./middlewares/logger');

// 初始化express应用
const app = express();

// 根据配置决定是否初始化数据库
if (config.server.enableDatabase) {
  initDatabase();
}

// 中间件
app.use(helmet()); // 安全头
app.use(cors()); // 跨域
app.use(express.json({ limit: '10mb' })); // 解析JSON，限制大小为10MB
app.use(express.urlencoded({ extended: true }));
app.use(morgan('combined')); // HTTP请求日志
app.use(logger); // 自定义日志中间件

// 添加一个简单的测试路由
app.get('/', (req, res) => {
  res.json({
    success: true,
    message: 'Redis未授权远程检测服务器API',
    version: '1.0.0',
    database_enabled: config.server.enableDatabase
  });
});

// API路由
app.use('/api/scan', scanRoutes);
app.use('/api/features', featureRoutes);

// 404处理
app.use((req, res) => {
  res.status(404).json({ 
    success: false, 
    message: '请求的资源不存在' 
  });
});

// 错误处理中间件
app.use(errorHandler);

module.exports = app; 