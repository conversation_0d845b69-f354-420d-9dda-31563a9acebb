require('dotenv').config();

module.exports = {
  // 服务器配置
  server: {
    port: process.env.PORT || 3000,
    nodeEnv: process.env.NODE_ENV || 'development',
    enableDatabase: process.env.ENABLE_DATABASE === 'false' || true
  },
  
  // 数据库配置
  database: {
    host: process.env.DB_HOST || '*************',
    user: process.env.DB_USER || 'root',
    password: process.env.DB_PASSWORD || 'fogonroad',
    database: process.env.DB_NAME || 'redis_scanner',
    port: process.env.DB_PORT || 3306
  },
  
  // API 密钥 (由开发者手动配置)
  apiKey: 'redis_scanner_api_key_2025' // 默认API密钥，建议在生产环境中更改为更复杂的值
}; 